/* Exercise 4: Story Readiness Assessment Styles */

.exercise-4 {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

/* Start Screen */
.exercise-4 .exercise-start {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.exercise-4 .categories-preview {
  margin: 30px 0;
  text-align: left;
}

.exercise-4 .category-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.exercise-4 .category-preview {
  padding: 15px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  background: #f9f9f9;
}

.exercise-4 .category-preview strong {
  display: block;
  color: #333;
  margin-bottom: 8px;
  font-size: 1.1em;
}

.exercise-4 .category-preview p {
  color: #666;
  margin: 0;
  font-size: 0.9em;
}

/* Categorization Screen */
.exercise-4 .categorization-screen {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.exercise-4 .categorization-header {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.exercise-4 .progress-indicator {
  margin-top: 10px;
  font-weight: bold;
  color: #007bff;
}

.exercise-4 .categorization-workspace {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
  min-height: 600px;
}

/* Categories Section */
.exercise-4 .categories-section h3 {
  margin-bottom: 20px;
  color: #333;
}

.exercise-4 .category-buckets {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.exercise-4 .category-bucket {
  border: 3px dashed #ddd;
  border-radius: 12px;
  padding: 15px;
  min-height: 200px;
  transition: all 0.3s ease;
  background: #fafafa;
}

.exercise-4 .category-bucket:hover,
.exercise-4 .category-bucket.drag-over {
  border-color: #007bff;
  background: #f0f8ff;
  transform: scale(1.02);
}

.exercise-4 .category-bucket.ready-to-size {
  border-color: #28a745;
}

.exercise-4 .category-bucket.too-vague {
  border-color: #ffc107;
}

.exercise-4 .category-bucket.too-large {
  border-color: #dc3545;
}

.exercise-4 .category-bucket.too-small {
  border-color: #6f42c1;
}

.exercise-4 .category-header {
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.exercise-4 .category-header h4 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 1.1em;
}

.exercise-4 .category-header p {
  margin: 0;
  color: #666;
  font-size: 0.85em;
}

.exercise-4 .story-count {
  float: right;
  background: #007bff;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8em;
  font-weight: bold;
}

.exercise-4 .category-stories {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.exercise-4 .categorized-story {
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 10px;
  font-size: 0.9em;
}

.exercise-4 .categorized-story strong {
  display: block;
  margin-bottom: 5px;
  color: #333;
}

.exercise-4 .categorized-story p {
  margin: 0;
  color: #666;
  font-size: 0.85em;
}

/* Stories Section */
.exercise-4 .stories-section h3 {
  margin-bottom: 20px;
  color: #333;
}

.exercise-4 .uncategorized-stories {
  display: flex;
  flex-direction: column;
  gap: 15px;
  max-height: 600px;
  overflow-y: auto;
  padding-right: 10px;
}

.exercise-4 .story-card {
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  cursor: grab;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.exercise-4 .story-card:hover {
  border-color: #007bff;
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

.exercise-4 .story-card:active {
  cursor: grabbing;
}

.exercise-4 .story-card.dragging {
  opacity: 0.8;
  transform: rotate(5deg);
  box-shadow: 0 8px 16px rgba(0,0,0,0.2);
}

.exercise-4 .story-header strong {
  color: #333;
  font-size: 1.1em;
}

.exercise-4 .story-description {
  margin: 10px 0;
  color: #666;
  font-size: 0.95em;
  line-height: 1.4;
}

.exercise-4 .acceptance-criteria {
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px solid #eee;
}

.exercise-4 .acceptance-criteria strong {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-size: 0.9em;
}

.exercise-4 .acceptance-criteria ul {
  margin: 0;
  padding-left: 20px;
}

.exercise-4 .acceptance-criteria li {
  margin-bottom: 4px;
  color: #666;
  font-size: 0.85em;
  line-height: 1.3;
}

/* Actions */
.exercise-4 .categorization-actions {
  text-align: center;
  padding: 20px;
}

.exercise-4 .submit-button {
  background: #007bff;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 6px;
  font-size: 1.1em;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.3s ease;
}

.exercise-4 .submit-button:hover:not(:disabled) {
  background: #0056b3;
}

.exercise-4 .submit-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Feedback Screen */
.exercise-4 .feedback-screen {
  max-width: 1000px;
  margin: 0 auto;
}

.exercise-4 .feedback-header {
  text-align: center;
  padding: 30px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 30px;
}

.exercise-4 .score-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20px;
}

.exercise-4 .score-value {
  font-size: 3em;
  font-weight: bold;
  color: #28a745;
}

.exercise-4 .score-label {
  font-size: 1.2em;
  color: #666;
  margin-top: 5px;
}

.exercise-4 .detailed-feedback {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.exercise-4 .category-feedback h3 {
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #eee;
}

.exercise-4 .story-feedback {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.exercise-4 .story-result {
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
}

.exercise-4 .story-result.correct {
  border-color: #28a745;
  background: #f8fff9;
}

.exercise-4 .story-result.incorrect {
  border-color: #dc3545;
  background: #fff8f8;
}

.exercise-4 .story-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.exercise-4 .story-info strong {
  color: #333;
  font-size: 1.1em;
}

.exercise-4 .result-indicator {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.9em;
  font-weight: bold;
}

.exercise-4 .result-indicator.correct {
  background: #28a745;
  color: white;
}

.exercise-4 .result-indicator.incorrect {
  background: #dc3545;
  color: white;
}

.exercise-4 .explanation p {
  margin: 10px 0;
  line-height: 1.5;
}

.exercise-4 .explanation strong {
  color: #333;
}

.exercise-4 .correct-category {
  background: #e7f3ff;
  padding: 10px;
  border-radius: 6px;
  margin: 10px 0;
}

.exercise-4 .techniques {
  background: #f0f8ff;
  padding: 15px;
  border-radius: 6px;
  margin: 10px 0;
}

.exercise-4 .techniques ul {
  margin: 10px 0 0 0;
  padding-left: 20px;
}

.exercise-4 .techniques li {
  margin-bottom: 8px;
  line-height: 1.4;
}

.exercise-4 .completion-actions {
  text-align: center;
  padding: 30px;
}

.exercise-4 .complete-button {
  background: #28a745;
  color: white;
  border: none;
  padding: 15px 40px;
  border-radius: 6px;
  font-size: 1.2em;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.3s ease;
}

.exercise-4 .complete-button:hover {
  background: #1e7e34;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .exercise-4 .categorization-workspace {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .exercise-4 .category-buckets {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .exercise-4 .category-buckets {
    grid-template-columns: 1fr;
  }
  
  .exercise-4 .category-list {
    grid-template-columns: 1fr;
  }
}
